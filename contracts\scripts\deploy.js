const { ethers } = require("hardhat");
const fs = require("fs");
const path = require("path");
require("dotenv").config();

async function main() {
  console.log("Starting PaymentGateway deployment...");
  console.log("=".repeat(50));

  // Get the deployer account
  const [deployer] = await ethers.getSigners();
  console.log("Deploying contracts with the account:", deployer.address);

  const balance = await deployer.getBalance();
  console.log("Account balance:", ethers.utils.formatEther(balance), "ETH");

  // Check minimum balance requirement
  const minBalance = ethers.utils.parseEther("0.1");
  if (balance.lt(minBalance)) {
    throw new Error(`Insufficient balance. Need at least 0.1 ETH, have ${ethers.utils.formatEther(balance)} ETH`);
  }

  // Get network information
  const network = await ethers.provider.getNetwork();
  console.log("Network:", network.name, "Chain ID:", network.chainId);

  let paymentTokenAddress;
  let mockTokenDeployed = false;

  // Determine payment token address based on network
  if (network.chainId === ********) { // Sepolia
    paymentTokenAddress = process.env.SEPOLIA_USDC_ADDRESS || "******************************************";
    console.log("Deploying to Sepolia testnet");
  } else if (network.chainId === 1) { // Mainnet
    paymentTokenAddress = process.env.MAINNET_USDC_ADDRESS || "******************************************";
    if (!paymentTokenAddress) {
      throw new Error("MAINNET_USDC_ADDRESS must be set for mainnet deployment");
    }
    console.log("Deploying to Ethereum mainnet");
    console.log("⚠️  WARNING: This is a MAINNET deployment!");
  } else {
    // For local testing, deploy a mock ERC20 token
    console.log("Deploying to local network, creating mock token...");
    const MockToken = await ethers.getContractFactory("MockERC20Permit");
    const mockToken = await MockToken.deploy("Mock USDC", "MUSDC", 6);
    await mockToken.deployed();
    paymentTokenAddress = mockToken.address;
    mockTokenDeployed = true;
    console.log("Mock token deployed to:", paymentTokenAddress);

    // Mint some tokens to deployer for testing
    const mintAmount = ethers.utils.parseUnits("10000", 6);
    await mockToken.mint(deployer.address, mintAmount);
    console.log("Minted", ethers.utils.formatUnits(mintAmount, 6), "MUSDC to deployer");
  }

  console.log("Using payment token address:", paymentTokenAddress);

  // Validate payment token contract
  console.log("\nValidating payment token contract...");
  try {
    const tokenContract = await ethers.getContractAt("IERC20", paymentTokenAddress);
    const tokenName = mockTokenDeployed ? "Mock USDC" : "Unknown";
    const tokenSymbol = mockTokenDeployed ? "MUSDC" : "Unknown";
    console.log("Token validation successful");
    console.log("Token Name:", tokenName);
    console.log("Token Symbol:", tokenSymbol);
  } catch (error) {
    console.error("Warning: Could not validate token contract:", error.message);
  }

  // Deploy PaymentGateway
  console.log("\nDeploying PaymentGateway contract...");
  const PaymentGateway = await ethers.getContractFactory("PaymentGateway");

  console.log("Estimating gas for deployment...");
  const deploymentData = PaymentGateway.getDeployTransaction(paymentTokenAddress);
  const gasEstimate = await ethers.provider.estimateGas(deploymentData);
  console.log("Estimated gas:", gasEstimate.toString());

  const paymentGateway = await PaymentGateway.deploy(paymentTokenAddress);
  console.log("Transaction hash:", paymentGateway.deployTransaction.hash);

  console.log("Waiting for deployment confirmation...");
  await paymentGateway.deployed();

  console.log("\n✅ PaymentGateway deployed successfully!");
  console.log("Contract address:", paymentGateway.address);
  console.log("Owner:", await paymentGateway.owner());
  console.log("Payment Token:", await paymentGateway.getPaymentToken());

  // Get deployment transaction details
  const deployTx = await ethers.provider.getTransaction(paymentGateway.deployTransaction.hash);
  const deployReceipt = await ethers.provider.getTransactionReceipt(paymentGateway.deployTransaction.hash);

  console.log("Gas used:", deployReceipt.gasUsed.toString());
  console.log("Gas price:", ethers.utils.formatUnits(deployTx.gasPrice, "gwei"), "gwei");
  console.log("Deployment cost:", ethers.utils.formatEther(deployReceipt.gasUsed.mul(deployTx.gasPrice)), "ETH");

  // Create comprehensive deployment info
  const deploymentInfo = {
    network: network.name,
    chainId: network.chainId,
    contracts: {
      paymentGateway: {
        address: paymentGateway.address,
        owner: await paymentGateway.owner(),
        paymentToken: paymentTokenAddress,
        deploymentTx: paymentGateway.deployTransaction.hash,
        blockNumber: deployReceipt.blockNumber,
        gasUsed: deployReceipt.gasUsed.toString(),
        gasPrice: deployTx.gasPrice.toString(),
        deploymentCost: ethers.utils.formatEther(deployReceipt.gasUsed.mul(deployTx.gasPrice))
      }
    },
    paymentToken: {
      address: paymentTokenAddress,
      isMock: mockTokenDeployed
    },
    deployer: {
      address: deployer.address,
      balanceAfter: ethers.utils.formatEther(await deployer.getBalance())
    },
    timestamp: new Date().toISOString(),
    hardhatVersion: require("hardhat/package.json").version
  };

  // Save deployment info to file
  const deploymentsDir = path.join(__dirname, "..", "deployments");
  if (!fs.existsSync(deploymentsDir)) {
    fs.mkdirSync(deploymentsDir, { recursive: true });
  }

  const deploymentFile = path.join(deploymentsDir, `${network.name}-${network.chainId}.json`);
  fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));

  console.log("\n📄 Deployment info saved to:", deploymentFile);
  console.log("\nDeployment Summary:");
  console.log("=".repeat(50));
  console.log(JSON.stringify(deploymentInfo, null, 2));

  // Verify contract on Etherscan if not local network
  if (network.chainId !== 31337 && process.env.ETHERSCAN_API_KEY) {
    console.log("\n🔍 Starting contract verification...");
    console.log("Waiting for block confirmations...");
    await paymentGateway.deployTransaction.wait(6);

    console.log("Verifying contract on Etherscan...");
    try {
      await hre.run("verify:verify", {
        address: paymentGateway.address,
        constructorArguments: [paymentTokenAddress],
      });
      console.log("✅ Contract verified successfully!");
      deploymentInfo.contracts.paymentGateway.verified = true;
      deploymentInfo.contracts.paymentGateway.etherscanUrl = `https://${network.chainId === 1 ? '' : 'sepolia.'}etherscan.io/address/${paymentGateway.address}`;
    } catch (error) {
      console.log("❌ Verification failed:", error.message);
      deploymentInfo.contracts.paymentGateway.verified = false;
      deploymentInfo.contracts.paymentGateway.verificationError = error.message;
    }

    // Update deployment file with verification info
    fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));
  } else if (network.chainId === 31337) {
    console.log("\n⚠️  Skipping verification for local network");
  } else {
    console.log("\n⚠️  Skipping verification - ETHERSCAN_API_KEY not set");
  }

  // Final deployment summary
  console.log("\n🎉 Deployment completed successfully!");
  console.log("=".repeat(50));
  console.log("PaymentGateway Address:", paymentGateway.address);
  console.log("Network:", network.name, `(Chain ID: ${network.chainId})`);
  console.log("Payment Token:", paymentTokenAddress);
  console.log("Owner:", await paymentGateway.owner());

  if (deploymentInfo.contracts.paymentGateway.etherscanUrl) {
    console.log("Etherscan URL:", deploymentInfo.contracts.paymentGateway.etherscanUrl);
  }

  console.log("\n📋 Next Steps:");
  console.log("1. Update your backend configuration with the contract address");
  console.log("2. Update your frontend configuration with the contract address");
  console.log("3. Test the contract functions on the deployed network");
  console.log("4. Set up monitoring and alerting for the contract");

  if (mockTokenDeployed) {
    console.log("\n⚠️  Note: Mock token deployed for testing purposes");
    console.log("   Mock Token Address:", paymentTokenAddress);
    console.log("   Remember to use real tokens for production deployment");
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
