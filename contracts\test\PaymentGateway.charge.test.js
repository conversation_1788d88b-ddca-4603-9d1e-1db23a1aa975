const { expect } = require("chai");
const { ethers } = require("hardhat");
const { loadFixture } = require("@nomicfoundation/hardhat-network-helpers");

describe("PaymentGateway - Charge Functions", function () {
  // Fixture to deploy contracts and setup initial state
  async function deployAndSetupFixture() {
    const [owner, user1, user2, nonOwner] = await ethers.getSigners();

    // Deploy mock ERC20 token with permit
    const MockToken = await ethers.getContractFactory("MockERC20Permit");
    const mockToken = await MockToken.deploy("Mock USDC", "MUSDC", 6);

    // Deploy PaymentGateway
    const PaymentGateway = await ethers.getContractFactory("PaymentGateway");
    const paymentGateway = await PaymentGateway.deploy(mockToken.address);

    // Mint tokens to users
    const mintAmount = ethers.utils.parseUnits("1000", 6);
    await mockToken.mint(user1.address, mintAmount);
    await mockToken.mint(user2.address, mintAmount);

    // Setup user1 with prepaid balance
    const depositAmount = ethers.utils.parseUnits("200", 6);
    await mockToken.connect(user1).approve(paymentGateway.address, depositAmount);
    await paymentGateway.connect(user1).deposit(depositAmount);

    return { paymentGateway, mockToken, owner, user1, user2, nonOwner, depositAmount };
  }

  describe("Charge Function (Prepaid)", function () {
    it("Should allow owner to charge user from prepaid balance", async function () {
      const { paymentGateway, owner, user1 } = await loadFixture(deployAndSetupFixture);
      
      const chargeAmount = ethers.utils.parseUnits("50", 6);
      const initialBalance = await paymentGateway.getPrepaidBalance(user1.address);
      
      await expect(paymentGateway.connect(owner).charge(user1.address, chargeAmount))
        .to.emit(paymentGateway, "PaymentMade")
        .withArgs(user1.address, chargeAmount);
      
      // Check balance after charge
      expect(await paymentGateway.getPrepaidBalance(user1.address))
        .to.equal(initialBalance.sub(chargeAmount));
    });

    it("Should revert if non-owner tries to charge", async function () {
      const { paymentGateway, user1, nonOwner } = await loadFixture(deployAndSetupFixture);
      
      const chargeAmount = ethers.utils.parseUnits("50", 6);
      
      await expect(
        paymentGateway.connect(nonOwner).charge(user1.address, chargeAmount)
      ).to.be.revertedWith("Ownable: caller is not the owner");
    });

    it("Should revert if charge amount is zero", async function () {
      const { paymentGateway, owner, user1 } = await loadFixture(deployAndSetupFixture);
      
      await expect(
        paymentGateway.connect(owner).charge(user1.address, 0)
      ).to.be.revertedWithCustomError(paymentGateway, "InvalidAmount");
    });

    it("Should revert if user has insufficient prepaid balance", async function () {
      const { paymentGateway, owner, user1, depositAmount } = await loadFixture(deployAndSetupFixture);
      
      const chargeAmount = depositAmount.add(ethers.utils.parseUnits("1", 6)); // More than deposited
      
      await expect(
        paymentGateway.connect(owner).charge(user1.address, chargeAmount)
      ).to.be.revertedWithCustomError(paymentGateway, "InsufficientBalance")
      .withArgs(chargeAmount, depositAmount);
    });

    it("Should handle multiple charges correctly", async function () {
      const { paymentGateway, owner, user1, depositAmount } = await loadFixture(deployAndSetupFixture);
      
      const chargeAmount1 = ethers.utils.parseUnits("30", 6);
      const chargeAmount2 = ethers.utils.parseUnits("20", 6);
      
      // First charge
      await paymentGateway.connect(owner).charge(user1.address, chargeAmount1);
      
      // Second charge
      await paymentGateway.connect(owner).charge(user1.address, chargeAmount2);
      
      // Check final balance
      expect(await paymentGateway.getPrepaidBalance(user1.address))
        .to.equal(depositAmount.sub(chargeAmount1).sub(chargeAmount2));
    });

    it("Should charge exact balance without issues", async function () {
      const { paymentGateway, owner, user1, depositAmount } = await loadFixture(deployAndSetupFixture);
      
      // Charge exact balance
      await paymentGateway.connect(owner).charge(user1.address, depositAmount);
      
      // Check balance is zero
      expect(await paymentGateway.getPrepaidBalance(user1.address)).to.equal(0);
    });
  });

  describe("Access Control", function () {
    it("Should only allow owner to call charge functions", async function () {
      const { paymentGateway, user1, user2 } = await loadFixture(deployAndSetupFixture);
      
      const chargeAmount = ethers.utils.parseUnits("10", 6);
      
      await expect(
        paymentGateway.connect(user2).charge(user1.address, chargeAmount)
      ).to.be.revertedWith("Ownable: caller is not the owner");
    });

    it("Should allow owner to transfer ownership", async function () {
      const { paymentGateway, owner, user1 } = await loadFixture(deployAndSetupFixture);
      
      // Transfer ownership
      await paymentGateway.connect(owner).transferOwnership(user1.address);
      
      // Check new owner
      expect(await paymentGateway.owner()).to.equal(user1.address);
      
      // New owner should be able to charge
      const chargeAmount = ethers.utils.parseUnits("10", 6);
      await expect(
        paymentGateway.connect(user1).charge(user1.address, chargeAmount)
      ).to.not.be.reverted;
    });
  });

  describe("Edge Cases", function () {
    it("Should handle charging user with zero balance", async function () {
      const { paymentGateway, owner, user2 } = await loadFixture(deployAndSetupFixture);
      
      const chargeAmount = ethers.utils.parseUnits("1", 6);
      
      await expect(
        paymentGateway.connect(owner).charge(user2.address, chargeAmount)
      ).to.be.revertedWithCustomError(paymentGateway, "InsufficientBalance")
      .withArgs(chargeAmount, 0);
    });

    it("Should handle very small amounts", async function () {
      const { paymentGateway, owner, user1 } = await loadFixture(deployAndSetupFixture);
      
      const chargeAmount = 1; // 1 wei
      const initialBalance = await paymentGateway.getPrepaidBalance(user1.address);
      
      await paymentGateway.connect(owner).charge(user1.address, chargeAmount);
      
      expect(await paymentGateway.getPrepaidBalance(user1.address))
        .to.equal(initialBalance.sub(chargeAmount));
    });
  });
});
