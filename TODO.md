# CryptoAI Chat - Development Plan

### Phase 1: Smart Contract & Core Backend Setup

- [x] **Smart Contract:** Design, write, and test the `PaymentGateway` smart contract in Solidity.
    - [x] Implement `prepaid` deposit and withdrawal functions.
    - [x] Implement ERC-2612 `permit` functionality for gas-less approvals.
    - [x] Implement core logic for debiting funds for services.
    - [x] Write comprehensive tests (Foundry or Hardhat).
- [ ] **Deployment:** Deploy the `PaymentGateway` contract to a testnet (e.g., Sepolia).
- [ ] **Backend Setup:** Initialize the Go project structure.
- [ ] **Configuration:** Set up configuration management for the backend (e.g., environment variables for RPC URLs, private keys, contract addresses).
- [ ] **Web3 Integration:** Integrate `go-ethereum` library to connect to the blockchain and interact with the deployed smart contract.

### Phase 2: Backend API Development

- [ ] **API Scaffolding:** Set up a web server (e.g., using Gin or Echo) and define the API routing structure.
- [ ] **User Endpoint:** Create an endpoint to fetch user balance from the smart contract.
- [ ] **Permit Endpoint:** Create an endpoint to receive and store the user's signed `permit` message.
- [ ] **Chat Endpoint:**
    - [ ] Design the core chat logic endpoint.
    - [ ] Integrate with the chosen AI model (e.g., Gemini API).
    - [ ] Implement payment verification logic before responding.
    - [ ] Implement the debiting logic (for both Prepaid and Permit methods) after a successful response.
- [ ] **Biconomy Integration (Backend):** Integrate Biconomy's SDK/API for transaction relaying if needed (e.g., for debiting from user's allowance).

### Phase 3: Frontend Development

- [ ] **Project Setup:** Initialize the Next.js project.
- [ ] **UI/UX:** Design and build the chat interface, inspired by Open WebUI.
- [ ] **Wallet Connection:** Integrate a library like `wagmi` or `web3-react` to handle wallet connections (MetaMask, etc.).
- [ ] **Biconomy Integration (Frontend):**
    - [ ] Integrate the Biconomy SDK for abstracting transactions.
    - [ ] Implement the UI flow for the `Prepaid` deposit.
    - [ ] Implement the UI flow for signing the `Permit` message.
- [ ] **API Integration:** Connect the frontend UI to the Go backend APIs.
    - [ ] Fetch and display user balance.
    - [ ] Send chat messages and render responses.
    - [ ] Trigger payment flows.

### Phase 4: Integration, Testing & Deployment

- [ ] **End-to-End Testing:** Thoroughly test the entire workflow from the user's perspective.
- [ ] **Security Audit:** Review the smart contract and backend for potential security vulnerabilities.
- [ ] **Deployment:**
    - [ ] Deploy the Go backend to a cloud service (e.g., GCP, AWS).
    - [ ] Deploy the Next.js frontend to a hosting platform (e.g., Vercel, Netlify).
- [ ] **Mainnet:**
    - [ ] Deploy the smart contract to the mainnet.
    - [ ] Update backend and frontend configurations to point to mainnet resources.
