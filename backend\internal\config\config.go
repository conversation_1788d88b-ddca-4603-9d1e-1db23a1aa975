package config

import (
	"github.com/spf13/viper"
)

// Config stores all configuration of the application.
// The values are read by viper from a config file or environment variable.
type Config struct {
	RPC_URL         string `mapstructure:"RPC_URL"`
	PRIVATE_KEY     string `mapstructure:"PRIVATE_KEY"`
	CONTRACT_ADDRESS string `mapstructure:"CONTRACT_ADDRESS"`
}

// LoadConfig reads configuration from file or environment variables.
func LoadConfig(path string) (config Config, err error) {
	// TODO: Implement logic to load configuration
	return
}
