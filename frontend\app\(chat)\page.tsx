'use client';

import React, { useState } from 'react';
// import { ChatWindow } from '@/components/ChatWindow';
// import { useAccount } from 'wagmi';

export default function ChatPage() {
    // const { address, isConnected } = useAccount();
    const [messages, setMessages] = useState([]);

    const handleSendMessage = async (prompt: string) => {
        // TODO: Add user message to state
        // TODO: Call our backend's /api/v1/chat endpoint
        // TODO: Add AI response to state
    };

    const handleDeposit = async () => {
        // TODO: Open payment modal and trigger Biconomy prepaid deposit flow
    };

    const handlePermit = async () => {
        // TODO: Trigger Biconomy permit signing flow
    };

    // if (!isConnected) {
    //     return <div>Please connect your wallet.</div>;
    // }

    return (
        <div>
            <h1>CryptoAI Chat</h1>
            {/* <ChatWindow messages={messages} onSendMessage={handleSendMessage} /> */}
            {/* Add buttons for Deposit and Permit */}
        </div>
    );
}
