const { expect } = require("chai");
const { ethers } = require("hardhat");
const { loadFixture, time } = require("@nomicfoundation/hardhat-network-helpers");

describe("PaymentGateway - Permit Functions", function () {
  // Fixture to deploy contracts
  async function deployPermitFixture() {
    const [owner, user1, user2, nonOwner] = await ethers.getSigners();

    // Deploy mock ERC20 token with permit
    const MockToken = await ethers.getContractFactory("MockERC20Permit");
    const mockToken = await MockToken.deploy("Mock USDC", "MUSDC", 6);

    // Deploy PaymentGateway
    const PaymentGateway = await ethers.getContractFactory("PaymentGateway");
    const paymentGateway = await PaymentGateway.deploy(mockToken.address);

    // Mint tokens to users
    const mintAmount = ethers.utils.parseUnits("1000", 6);
    await mockToken.mint(user1.address, mintAmount);
    await mockToken.mint(user2.address, mintAmount);

    return { paymentGateway, mockToken, owner, user1, user2, nonOwner };
  }

  // Helper function to create permit signature
  async function createPermitSignature(token, owner, spender, value, deadline, signer) {
    const nonce = await token.nonces(owner.address);
    const name = await token.name();
    const version = "1";
    const chainId = await signer.getChainId();

    const domain = {
      name,
      version,
      chainId,
      verifyingContract: token.address,
    };

    const types = {
      Permit: [
        { name: "owner", type: "address" },
        { name: "spender", type: "address" },
        { name: "value", type: "uint256" },
        { name: "nonce", type: "uint256" },
        { name: "deadline", type: "uint256" },
      ],
    };

    const values = {
      owner: owner.address,
      spender: spender,
      value,
      nonce,
      deadline,
    };

    const signature = await signer._signTypedData(domain, types, values);
    const { v, r, s } = ethers.utils.splitSignature(signature);

    return { v, r, s, deadline, nonce };
  }

  describe("ChargeWithPermit Function", function () {
    it("Should allow owner to charge user using permit signature", async function () {
      const { paymentGateway, mockToken, owner, user1 } = await loadFixture(deployPermitFixture);
      
      const chargeAmount = ethers.utils.parseUnits("100", 6);
      const deadline = (await time.latest()) + 3600; // 1 hour from now
      
      // Create permit signature
      const { v, r, s } = await createPermitSignature(
        mockToken,
        user1,
        paymentGateway.address,
        chargeAmount,
        deadline,
        user1
      );
      
      const initialUserBalance = await mockToken.balanceOf(user1.address);
      const initialContractBalance = await mockToken.balanceOf(paymentGateway.address);
      
      // Execute chargeWithPermit
      await expect(
        paymentGateway.connect(owner).chargeWithPermit(
          user1.address,
          chargeAmount,
          deadline,
          v,
          r,
          s
        )
      ).to.emit(paymentGateway, "PermitPaymentMade")
      .withArgs(user1.address, chargeAmount);
      
      // Check balances
      expect(await mockToken.balanceOf(user1.address))
        .to.equal(initialUserBalance.sub(chargeAmount));
      expect(await mockToken.balanceOf(paymentGateway.address))
        .to.equal(initialContractBalance.add(chargeAmount));
    });

    it("Should revert if non-owner tries to use chargeWithPermit", async function () {
      const { paymentGateway, mockToken, user1, nonOwner } = await loadFixture(deployPermitFixture);
      
      const chargeAmount = ethers.utils.parseUnits("100", 6);
      const deadline = (await time.latest()) + 3600;
      
      const { v, r, s } = await createPermitSignature(
        mockToken,
        user1,
        paymentGateway.address,
        chargeAmount,
        deadline,
        user1
      );
      
      await expect(
        paymentGateway.connect(nonOwner).chargeWithPermit(
          user1.address,
          chargeAmount,
          deadline,
          v,
          r,
          s
        )
      ).to.be.revertedWith("Ownable: caller is not the owner");
    });

    it("Should revert if charge amount is zero", async function () {
      const { paymentGateway, mockToken, owner, user1 } = await loadFixture(deployPermitFixture);
      
      const chargeAmount = 0;
      const deadline = (await time.latest()) + 3600;
      
      const { v, r, s } = await createPermitSignature(
        mockToken,
        user1,
        paymentGateway.address,
        ethers.utils.parseUnits("100", 6), // Sign for non-zero amount
        deadline,
        user1
      );
      
      await expect(
        paymentGateway.connect(owner).chargeWithPermit(
          user1.address,
          chargeAmount,
          deadline,
          v,
          r,
          s
        )
      ).to.be.revertedWithCustomError(paymentGateway, "InvalidAmount");
    });

    it("Should revert if permit signature is expired", async function () {
      const { paymentGateway, mockToken, owner, user1 } = await loadFixture(deployPermitFixture);
      
      const chargeAmount = ethers.utils.parseUnits("100", 6);
      const deadline = (await time.latest()) - 1; // Expired deadline
      
      const { v, r, s } = await createPermitSignature(
        mockToken,
        user1,
        paymentGateway.address,
        chargeAmount,
        deadline,
        user1
      );
      
      await expect(
        paymentGateway.connect(owner).chargeWithPermit(
          user1.address,
          chargeAmount,
          deadline,
          v,
          r,
          s
        )
      ).to.be.revertedWith("ERC20Permit: expired deadline");
    });

    it("Should revert if permit signature is invalid", async function () {
      const { paymentGateway, mockToken, owner, user1, user2 } = await loadFixture(deployPermitFixture);
      
      const chargeAmount = ethers.utils.parseUnits("100", 6);
      const deadline = (await time.latest()) + 3600;
      
      // Create signature with wrong signer
      const { v, r, s } = await createPermitSignature(
        mockToken,
        user1,
        paymentGateway.address,
        chargeAmount,
        deadline,
        user2 // Wrong signer
      );
      
      await expect(
        paymentGateway.connect(owner).chargeWithPermit(
          user1.address,
          chargeAmount,
          deadline,
          v,
          r,
          s
        )
      ).to.be.revertedWith("ERC20Permit: invalid signature");
    });

    it("Should revert if user has insufficient token balance", async function () {
      const { paymentGateway, mockToken, owner, user1 } = await loadFixture(deployPermitFixture);
      
      const userBalance = await mockToken.balanceOf(user1.address);
      const chargeAmount = userBalance.add(ethers.utils.parseUnits("1", 6)); // More than balance
      const deadline = (await time.latest()) + 3600;
      
      const { v, r, s } = await createPermitSignature(
        mockToken,
        user1,
        paymentGateway.address,
        chargeAmount,
        deadline,
        user1
      );
      
      await expect(
        paymentGateway.connect(owner).chargeWithPermit(
          user1.address,
          chargeAmount,
          deadline,
          v,
          r,
          s
        )
      ).to.be.revertedWithCustomError(paymentGateway, "TransferFailed");
    });

    it("Should handle multiple permit charges correctly", async function () {
      const { paymentGateway, mockToken, owner, user1 } = await loadFixture(deployPermitFixture);
      
      const chargeAmount1 = ethers.utils.parseUnits("50", 6);
      const chargeAmount2 = ethers.utils.parseUnits("30", 6);
      const deadline = (await time.latest()) + 3600;
      
      const initialBalance = await mockToken.balanceOf(user1.address);
      
      // First permit charge
      const { v: v1, r: r1, s: s1 } = await createPermitSignature(
        mockToken,
        user1,
        paymentGateway.address,
        chargeAmount1,
        deadline,
        user1
      );
      
      await paymentGateway.connect(owner).chargeWithPermit(
        user1.address,
        chargeAmount1,
        deadline,
        v1,
        r1,
        s1
      );
      
      // Second permit charge (need new signature due to nonce increment)
      const { v: v2, r: r2, s: s2 } = await createPermitSignature(
        mockToken,
        user1,
        paymentGateway.address,
        chargeAmount2,
        deadline,
        user1
      );
      
      await paymentGateway.connect(owner).chargeWithPermit(
        user1.address,
        chargeAmount2,
        deadline,
        v2,
        r2,
        s2
      );
      
      // Check final balance
      expect(await mockToken.balanceOf(user1.address))
        .to.equal(initialBalance.sub(chargeAmount1).sub(chargeAmount2));
    });
  });
});
