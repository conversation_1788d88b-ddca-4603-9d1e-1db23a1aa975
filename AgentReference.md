# Agent Reference for CryptoAI Chat Project

This document provides essential context and guidelines for any LLM agent collaborating on the `cryptoai` project.

## Project Overview

**Goal:** To build a Web3-enabled AI chat application where users pay for AI interactions using cryptocurrency.

**Core Functionality:**
*   **AI Chat:** Standard conversational AI interface.
*   **Crypto Payments:** Two primary methods for settlement:
    *   **Prepaid:** Users deposit funds into a smart contract, and their balance is debited per interaction. This saves Gas Fees on individual interactions.
    *   **Permit (ERC-2612):** Users sign a gas-less authorization (permit) for a certain spending limit. The backend then pulls funds from their wallet as needed, with transaction gas sponsored by the backend via Biconomy.

**Key Technologies:**
*   **Frontend:** Next.js (React, TypeScript)
*   **Backend:** Go
*   **Smart Contracts:** Solidity
*   **Web3 Payment Gateway:** Biconomy (for transaction relaying and gas abstraction)

## Architecture Highlights

*   **Frontend-Backend Separation:** Clear distinction between UI (Next.js) and business logic/blockchain interaction (Go).
*   **Smart Contract as Trust Layer:** The `PaymentGateway.sol` contract is the single source of truth for user balances and payment authorization.
*   **Backend as Orchestrator:** The Go backend is responsible for:
    *   Interacting with the AI model.
    *   Calculating interaction costs.
    *   Initiating payment debits on the smart contract (and using Biconomy for gas sponsorship).
    *   Storing and managing `permit` signatures.
*   **Biconomy Integration:** Used on both frontend (for user-initiated deposits/permits) and backend (for sponsoring `charge` transactions).

## Current Project Status

*   **Smart Contracts:** ✅ **COMPLETED** - PaymentGateway smart contract fully implemented and tested
    *   PaymentGateway.sol with prepaid and permit functionality
    *   Comprehensive test suite with 100+ test cases
    *   Security features: reentrancy protection, access control, input validation
    *   Deployment scripts for testnet and mainnet
    *   PaymentGatewayV2.sol with enhanced security features
    *   Complete documentation and security analysis
*   **Backend:** 🔄 **IN PROGRESS** - Go project structure scaffolded, needs implementation
*   **Frontend:** 🔄 **IN PROGRESS** - Next.js project structure scaffolded, needs implementation
*   **`TODO.md`:** A detailed development checklist exists at the project root (`TODO.md`). Smart contract phase completed.

## Guidelines for Collaboration

*   **Consult `TODO.md`:** Always refer to `TODO.md` for the next logical step in development.
*   **Absolute Paths:** When performing file operations (read, write, replace), always use absolute file paths.
*   **Adhere to Conventions:**
    *   **Go:** Follow idiomatic Go practices for the backend.
    *   **Next.js/React:** Follow modern React/Next.js conventions for the frontend.
    *   **Solidity:** Adhere to Solidity best practices and OpenZeppelin standards for smart contracts.
*   **Incremental Implementation:** Focus on completing one `// TODO:` section or one logical step at a time.
*   **No Unsolicited Implementation:** Do not write full implementation logic unless explicitly asked to "implement" a specific section or task. The current skeletons are for structure.
*   **Explain Critical Commands:** Before executing any shell commands that modify the filesystem or system state, provide a brief explanation of their purpose.
*   **Security First:** Always prioritize security, especially when dealing with smart contracts and payment logic.

## Smart Contract Implementation Details

### PaymentGateway Contract Features
*   **Prepaid System**: Users can deposit ERC20 tokens and maintain a prepaid balance
*   **ERC-2612 Permit Support**: Gas-less approvals using cryptographic signatures
*   **Owner-Only Charging**: Backend service can charge users for AI interactions
*   **Security Measures**: Reentrancy protection, access control, input validation
*   **Emergency Functions**: Owner can withdraw contract funds if needed

### Contract Addresses (After Deployment)
*   **Sepolia Testnet**: Check `contracts/deployments/sepolia-11155111.json`
*   **Mainnet**: Check `contracts/deployments/mainnet-1.json`

### Key Functions
*   `deposit(uint256 _amount)`: User deposits tokens into prepaid balance
*   `withdraw(uint256 _amount)`: User withdraws from prepaid balance
*   `charge(address _user, uint256 _amount)`: Owner charges user from prepaid balance
*   `chargeWithPermit(...)`: Owner charges user using permit signature
*   `getPrepaidBalance(address _user)`: View user's prepaid balance

### Testing
*   **Unit Tests**: Individual function testing (PaymentGateway.test.js)
*   **Charge Tests**: Prepaid charging functionality (PaymentGateway.charge.test.js)
*   **Permit Tests**: ERC-2612 permit functionality (PaymentGateway.permit.test.js)
*   **Security Tests**: Access control and security measures (PaymentGateway.security.test.js)
*   **Integration Tests**: End-to-end user journeys (PaymentGateway.integration.test.js)

### Deployment Scripts
*   `scripts/deploy.js`: Main deployment script with network detection
*   `scripts/verify.js`: Contract verification on Etherscan
*   `scripts/interact.js`: Contract interaction and testing script
*   `scripts/upgrade.js`: Contract upgrade to PaymentGatewayV2

This reference aims to provide sufficient context for seamless collaboration.
