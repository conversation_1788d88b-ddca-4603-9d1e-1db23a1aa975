{"name": "cryptoai-contracts", "version": "1.0.0", "description": "Smart contracts for CryptoAI Chat application", "main": "index.js", "scripts": {"compile": "hardhat compile", "test": "hardhat test", "test:gas": "REPORT_GAS=true hardhat test", "test:coverage": "hardhat coverage", "deploy:local": "hardhat run scripts/deploy.js --network localhost", "deploy:sepolia": "hardhat run scripts/deploy.js --network sepolia", "deploy:mainnet": "hardhat run scripts/deploy.js --network mainnet", "verify:sepolia": "hardhat run scripts/verify.js --network sepolia", "verify:mainnet": "hardhat run scripts/verify.js --network mainnet", "interact:local": "hardhat run scripts/interact.js --network localhost", "interact:sepolia": "hardhat run scripts/interact.js --network sepolia", "interact:mainnet": "hardhat run scripts/interact.js --network mainnet", "upgrade:sepolia": "hardhat run scripts/upgrade.js --network sepolia", "upgrade:mainnet": "hardhat run scripts/upgrade.js --network mainnet", "node": "hardhat node", "clean": "hardhat clean", "size": "hardhat size-contracts", "flatten": "hardhat flatten"}, "keywords": ["solidity", "ethereum", "smart-contracts", "web3", "crypto", "ai"], "author": "CryptoAI Team", "license": "MIT", "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^4.0.0", "@nomicfoundation/hardhat-verify": "^2.0.0", "@openzeppelin/contracts": "^5.0.0", "hardhat": "^2.19.0", "hardhat-gas-reporter": "^1.0.9", "solidity-coverage": "^0.8.5"}, "dependencies": {"dotenv": "^16.3.1"}}