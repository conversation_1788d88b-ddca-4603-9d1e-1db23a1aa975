const { ethers } = require("hardhat");
const fs = require("fs");
const path = require("path");
require("dotenv").config();

async function main() {
  console.log("PaymentGateway Contract Interaction Script");
  console.log("=".repeat(50));
  
  // Get network information
  const network = await ethers.provider.getNetwork();
  console.log("Network:", network.name, "Chain ID:", network.chainId);
  
  // Load deployment info
  const deploymentsDir = path.join(__dirname, "..", "deployments");
  const deploymentFile = path.join(deploymentsDir, `${network.name}-${network.chainId}.json`);
  
  if (!fs.existsSync(deploymentFile)) {
    throw new Error(`Deployment file not found: ${deploymentFile}`);
  }
  
  const deploymentInfo = JSON.parse(fs.readFileSync(deploymentFile, 'utf8'));
  const contractAddress = deploymentInfo.contracts.paymentGateway.address;
  const paymentTokenAddress = deploymentInfo.paymentToken.address;
  
  console.log("Contract Address:", contractAddress);
  console.log("Payment Token:", paymentTokenAddress);
  
  // Get signers
  const [deployer, user1, user2] = await ethers.getSigners();
  console.log("Deployer:", deployer.address);
  console.log("User1:", user1.address);
  console.log("User2:", user2.address);
  
  // Connect to contracts
  const paymentGateway = await ethers.getContractAt("PaymentGateway", contractAddress);
  const paymentToken = await ethers.getContractAt("IERC20", paymentTokenAddress);
  
  console.log("\n📊 Contract Information:");
  console.log("Owner:", await paymentGateway.owner());
  console.log("Payment Token:", await paymentGateway.getPaymentToken());
  
  // Check if it's a mock token (local network)
  if (deploymentInfo.paymentToken.isMock) {
    console.log("\n🪙 Mock Token Operations:");
    const mockToken = await ethers.getContractAt("MockERC20Permit", paymentTokenAddress);
    
    // Mint tokens to users for testing
    const mintAmount = ethers.utils.parseUnits("1000", 6);
    console.log("Minting", ethers.utils.formatUnits(mintAmount, 6), "tokens to users...");
    
    await mockToken.mint(user1.address, mintAmount);
    await mockToken.mint(user2.address, mintAmount);
    
    console.log("✅ Tokens minted successfully");
  }
  
  // Check balances
  console.log("\n💰 Token Balances:");
  const user1Balance = await paymentToken.balanceOf(user1.address);
  const user2Balance = await paymentToken.balanceOf(user2.address);
  console.log("User1 Token Balance:", ethers.utils.formatUnits(user1Balance, 6));
  console.log("User2 Token Balance:", ethers.utils.formatUnits(user2Balance, 6));
  
  // Check prepaid balances
  console.log("\n🏦 Prepaid Balances:");
  const user1Prepaid = await paymentGateway.getPrepaidBalance(user1.address);
  const user2Prepaid = await paymentGateway.getPrepaidBalance(user2.address);
  console.log("User1 Prepaid Balance:", ethers.utils.formatUnits(user1Prepaid, 6));
  console.log("User2 Prepaid Balance:", ethers.utils.formatUnits(user2Prepaid, 6));
  
  // Demonstrate deposit functionality
  if (user1Balance.gt(0)) {
    console.log("\n💳 Testing Deposit Functionality:");
    const depositAmount = ethers.utils.parseUnits("100", 6);
    
    console.log("Approving tokens for deposit...");
    await paymentToken.connect(user1).approve(contractAddress, depositAmount);
    
    console.log("Making deposit...");
    const depositTx = await paymentGateway.connect(user1).deposit(depositAmount);
    await depositTx.wait();
    
    console.log("✅ Deposit successful!");
    console.log("Transaction hash:", depositTx.hash);
    
    // Check updated balance
    const newPrepaidBalance = await paymentGateway.getPrepaidBalance(user1.address);
    console.log("New prepaid balance:", ethers.utils.formatUnits(newPrepaidBalance, 6));
  }
  
  // Demonstrate charge functionality (owner only)
  const currentOwner = await paymentGateway.owner();
  if (currentOwner.toLowerCase() === deployer.address.toLowerCase()) {
    console.log("\n⚡ Testing Charge Functionality:");
    const chargeAmount = ethers.utils.parseUnits("10", 6);
    const userPrepaidBalance = await paymentGateway.getPrepaidBalance(user1.address);
    
    if (userPrepaidBalance.gte(chargeAmount)) {
      console.log("Charging user for AI interaction...");
      const chargeTx = await paymentGateway.connect(deployer).charge(user1.address, chargeAmount);
      await chargeTx.wait();
      
      console.log("✅ Charge successful!");
      console.log("Transaction hash:", chargeTx.hash);
      
      // Check updated balance
      const newPrepaidBalance = await paymentGateway.getPrepaidBalance(user1.address);
      console.log("New prepaid balance:", ethers.utils.formatUnits(newPrepaidBalance, 6));
    } else {
      console.log("⚠️ Insufficient prepaid balance for charge");
    }
  } else {
    console.log("\n⚠️ Skipping charge test - not contract owner");
  }
  
  // Contract statistics
  console.log("\n📈 Contract Statistics:");
  const contractTokenBalance = await paymentToken.balanceOf(contractAddress);
  console.log("Contract Token Balance:", ethers.utils.formatUnits(contractTokenBalance, 6));
  
  // Get recent events
  console.log("\n📋 Recent Events:");
  try {
    const currentBlock = await ethers.provider.getBlockNumber();
    const fromBlock = Math.max(0, currentBlock - 1000); // Last 1000 blocks
    
    const depositEvents = await paymentGateway.queryFilter("Deposit", fromBlock);
    const withdrawalEvents = await paymentGateway.queryFilter("Withdrawal", fromBlock);
    const paymentEvents = await paymentGateway.queryFilter("PaymentMade", fromBlock);
    
    console.log("Recent Deposits:", depositEvents.length);
    console.log("Recent Withdrawals:", withdrawalEvents.length);
    console.log("Recent Payments:", paymentEvents.length);
    
    // Show last few events
    if (depositEvents.length > 0) {
      console.log("\nLast Deposit:");
      const lastDeposit = depositEvents[depositEvents.length - 1];
      console.log("  User:", lastDeposit.args.user);
      console.log("  Amount:", ethers.utils.formatUnits(lastDeposit.args.amount, 6));
      console.log("  Block:", lastDeposit.blockNumber);
    }
    
  } catch (error) {
    console.log("Could not fetch events:", error.message);
  }
  
  console.log("\n✅ Contract interaction completed successfully!");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
