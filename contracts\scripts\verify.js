const { ethers } = require("hardhat");
const fs = require("fs");
const path = require("path");
require("dotenv").config();

async function main() {
  console.log("Starting contract verification...");
  
  // Get network information
  const network = await ethers.provider.getNetwork();
  console.log("Network:", network.name, "Chain ID:", network.chainId);
  
  // Load deployment info
  const deploymentsDir = path.join(__dirname, "..", "deployments");
  const deploymentFile = path.join(deploymentsDir, `${network.name}-${network.chainId}.json`);
  
  if (!fs.existsSync(deploymentFile)) {
    throw new Error(`Deployment file not found: ${deploymentFile}`);
  }
  
  const deploymentInfo = JSON.parse(fs.readFileSync(deploymentFile, 'utf8'));
  const contractAddress = deploymentInfo.contracts.paymentGateway.address;
  const paymentTokenAddress = deploymentInfo.paymentToken.address;
  
  console.log("Contract Address:", contractAddress);
  console.log("Payment Token:", paymentTokenAddress);
  
  if (!process.env.ETHERSCAN_API_KEY) {
    throw new Error("ETHERSCAN_API_KEY not set in environment variables");
  }
  
  try {
    console.log("Verifying PaymentGateway contract...");
    await hre.run("verify:verify", {
      address: contractAddress,
      constructorArguments: [paymentTokenAddress],
    });
    
    console.log("✅ Contract verified successfully!");
    
    // Update deployment info
    deploymentInfo.contracts.paymentGateway.verified = true;
    deploymentInfo.contracts.paymentGateway.verifiedAt = new Date().toISOString();
    deploymentInfo.contracts.paymentGateway.etherscanUrl = `https://${network.chainId === 1 ? '' : 'sepolia.'}etherscan.io/address/${contractAddress}`;
    
    fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));
    console.log("Updated deployment file with verification info");
    
  } catch (error) {
    console.error("❌ Verification failed:", error.message);
    
    // Update deployment info with error
    deploymentInfo.contracts.paymentGateway.verified = false;
    deploymentInfo.contracts.paymentGateway.verificationError = error.message;
    deploymentInfo.contracts.paymentGateway.verificationAttemptedAt = new Date().toISOString();
    
    fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));
    
    throw error;
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
