const { expect } = require("chai");
const { ethers } = require("hardhat");
const { loadFixture } = require("@nomicfoundation/hardhat-network-helpers");

describe("PaymentGateway - Security Tests", function () {
  async function deploySecurityFixture() {
    const [owner, user1, user2, attacker, newOwner] = await ethers.getSigners();

    // Deploy mock ERC20 token with permit
    const MockToken = await ethers.getContractFactory("MockERC20Permit");
    const mockToken = await MockToken.deploy("Mock USDC", "MUSDC", 6);

    // Deploy PaymentGateway
    const PaymentGateway = await ethers.getContractFactory("PaymentGateway");
    const paymentGateway = await PaymentGateway.deploy(mockToken.address);

    // Mint tokens to users
    const mintAmount = ethers.utils.parseUnits("1000", 6);
    await mockToken.mint(user1.address, mintAmount);
    await mockToken.mint(user2.address, mintAmount);
    await mockToken.mint(attacker.address, mintAmount);

    return { paymentGateway, mockToken, owner, user1, user2, attacker, newOwner };
  }

  describe("Access Control Security", function () {
    it("Should prevent non-owner from calling charge function", async function () {
      const { paymentGateway, attacker, user1 } = await loadFixture(deploySecurityFixture);
      
      const chargeAmount = ethers.utils.parseUnits("10", 6);
      
      await expect(
        paymentGateway.connect(attacker).charge(user1.address, chargeAmount)
      ).to.be.revertedWith("Ownable: caller is not the owner");
    });

    it("Should prevent non-owner from calling chargeWithPermit function", async function () {
      const { paymentGateway, attacker, user1 } = await loadFixture(deploySecurityFixture);
      
      const chargeAmount = ethers.utils.parseUnits("10", 6);
      const deadline = Math.floor(Date.now() / 1000) + 3600;
      
      await expect(
        paymentGateway.connect(attacker).chargeWithPermit(
          user1.address,
          chargeAmount,
          deadline,
          27, // dummy v
          ethers.utils.formatBytes32String("dummy"), // dummy r
          ethers.utils.formatBytes32String("dummy")  // dummy s
        )
      ).to.be.revertedWith("Ownable: caller is not the owner");
    });

    it("Should prevent non-owner from emergency withdraw", async function () {
      const { paymentGateway, attacker } = await loadFixture(deploySecurityFixture);
      
      const withdrawAmount = ethers.utils.parseUnits("10", 6);
      
      await expect(
        paymentGateway.connect(attacker).emergencyWithdraw(withdrawAmount)
      ).to.be.revertedWith("Ownable: caller is not the owner");
    });

    it("Should allow secure ownership transfer", async function () {
      const { paymentGateway, owner, newOwner, user1 } = await loadFixture(deploySecurityFixture);
      
      // Transfer ownership
      await paymentGateway.connect(owner).transferOwnership(newOwner.address);
      
      // Verify new owner
      expect(await paymentGateway.owner()).to.equal(newOwner.address);
      
      // Old owner should no longer have access
      const chargeAmount = ethers.utils.parseUnits("10", 6);
      await expect(
        paymentGateway.connect(owner).charge(user1.address, chargeAmount)
      ).to.be.revertedWith("Ownable: caller is not the owner");
      
      // New owner should have access
      // (This would fail due to insufficient balance, but access control passes)
      await expect(
        paymentGateway.connect(newOwner).charge(user1.address, chargeAmount)
      ).to.be.revertedWithCustomError(paymentGateway, "InsufficientBalance");
    });
  });

  describe("Reentrancy Protection", function () {
    it("Should prevent reentrancy on deposit function", async function () {
      // Note: This test verifies the nonReentrant modifier is in place
      // A full reentrancy test would require a malicious contract
      const { paymentGateway, mockToken, user1 } = await loadFixture(deploySecurityFixture);
      
      const depositAmount = ethers.utils.parseUnits("100", 6);
      await mockToken.connect(user1).approve(paymentGateway.address, depositAmount);
      
      // Normal deposit should work
      await expect(paymentGateway.connect(user1).deposit(depositAmount)).to.not.be.reverted;
    });

    it("Should prevent reentrancy on withdraw function", async function () {
      const { paymentGateway, mockToken, user1 } = await loadFixture(deploySecurityFixture);
      
      // Setup: deposit first
      const depositAmount = ethers.utils.parseUnits("100", 6);
      await mockToken.connect(user1).approve(paymentGateway.address, depositAmount);
      await paymentGateway.connect(user1).deposit(depositAmount);
      
      // Normal withdraw should work
      const withdrawAmount = ethers.utils.parseUnits("50", 6);
      await expect(paymentGateway.connect(user1).withdraw(withdrawAmount)).to.not.be.reverted;
    });
  });

  describe("Input Validation Security", function () {
    it("Should reject zero amounts in all functions", async function () {
      const { paymentGateway, owner, user1 } = await loadFixture(deploySecurityFixture);
      
      // Deposit with zero amount
      await expect(
        paymentGateway.connect(user1).deposit(0)
      ).to.be.revertedWithCustomError(paymentGateway, "InvalidAmount");
      
      // Withdraw with zero amount
      await expect(
        paymentGateway.connect(user1).withdraw(0)
      ).to.be.revertedWithCustomError(paymentGateway, "InvalidAmount");
      
      // Charge with zero amount
      await expect(
        paymentGateway.connect(owner).charge(user1.address, 0)
      ).to.be.revertedWithCustomError(paymentGateway, "InvalidAmount");
      
      // Emergency withdraw with zero amount
      await expect(
        paymentGateway.connect(owner).emergencyWithdraw(0)
      ).to.be.revertedWithCustomError(paymentGateway, "InvalidAmount");
    });

    it("Should reject invalid token address in constructor", async function () {
      const PaymentGateway = await ethers.getContractFactory("PaymentGateway");
      
      await expect(
        PaymentGateway.deploy(ethers.constants.AddressZero)
      ).to.be.revertedWithCustomError(PaymentGateway, "InvalidToken");
    });
  });

  describe("Balance Protection", function () {
    it("Should prevent overdraft in charge function", async function () {
      const { paymentGateway, mockToken, owner, user1 } = await loadFixture(deploySecurityFixture);
      
      // Setup small balance
      const depositAmount = ethers.utils.parseUnits("10", 6);
      await mockToken.connect(user1).approve(paymentGateway.address, depositAmount);
      await paymentGateway.connect(user1).deposit(depositAmount);
      
      // Try to charge more than balance
      const chargeAmount = ethers.utils.parseUnits("20", 6);
      await expect(
        paymentGateway.connect(owner).charge(user1.address, chargeAmount)
      ).to.be.revertedWithCustomError(paymentGateway, "InsufficientBalance")
      .withArgs(chargeAmount, depositAmount);
    });

    it("Should prevent overdraft in withdraw function", async function () {
      const { paymentGateway, mockToken, user1 } = await loadFixture(deploySecurityFixture);
      
      // Setup small balance
      const depositAmount = ethers.utils.parseUnits("10", 6);
      await mockToken.connect(user1).approve(paymentGateway.address, depositAmount);
      await paymentGateway.connect(user1).deposit(depositAmount);
      
      // Try to withdraw more than balance
      const withdrawAmount = ethers.utils.parseUnits("20", 6);
      await expect(
        paymentGateway.connect(user1).withdraw(withdrawAmount)
      ).to.be.revertedWithCustomError(paymentGateway, "InsufficientBalance")
      .withArgs(withdrawAmount, depositAmount);
    });
  });

  describe("Token Transfer Security", function () {
    it("Should handle failed token transfers gracefully", async function () {
      const { paymentGateway, user1 } = await loadFixture(deploySecurityFixture);
      
      // Try to deposit without approval (should fail)
      const depositAmount = ethers.utils.parseUnits("100", 6);
      await expect(
        paymentGateway.connect(user1).deposit(depositAmount)
      ).to.be.revertedWithCustomError(paymentGateway, "TransferFailed");
    });

    it("Should prevent deposits exceeding user token balance", async function () {
      const { paymentGateway, mockToken, user1 } = await loadFixture(deploySecurityFixture);
      
      const userBalance = await mockToken.balanceOf(user1.address);
      const depositAmount = userBalance.add(ethers.utils.parseUnits("1", 6));
      
      await mockToken.connect(user1).approve(paymentGateway.address, depositAmount);
      
      await expect(
        paymentGateway.connect(user1).deposit(depositAmount)
      ).to.be.revertedWithCustomError(paymentGateway, "TransferFailed");
    });
  });

  describe("State Consistency", function () {
    it("Should maintain consistent state during failed operations", async function () {
      const { paymentGateway, mockToken, owner, user1 } = await loadFixture(deploySecurityFixture);
      
      // Setup initial state
      const depositAmount = ethers.utils.parseUnits("100", 6);
      await mockToken.connect(user1).approve(paymentGateway.address, depositAmount);
      await paymentGateway.connect(user1).deposit(depositAmount);
      
      const initialBalance = await paymentGateway.getPrepaidBalance(user1.address);
      
      // Try invalid operation
      const invalidChargeAmount = depositAmount.add(ethers.utils.parseUnits("1", 6));
      await expect(
        paymentGateway.connect(owner).charge(user1.address, invalidChargeAmount)
      ).to.be.revertedWithCustomError(paymentGateway, "InsufficientBalance");
      
      // State should remain unchanged
      expect(await paymentGateway.getPrepaidBalance(user1.address)).to.equal(initialBalance);
    });
  });

  describe("Event Emission Security", function () {
    it("Should emit events for all state-changing operations", async function () {
      const { paymentGateway, mockToken, owner, user1 } = await loadFixture(deploySecurityFixture);
      
      const amount = ethers.utils.parseUnits("100", 6);
      
      // Deposit should emit event
      await mockToken.connect(user1).approve(paymentGateway.address, amount);
      await expect(paymentGateway.connect(user1).deposit(amount))
        .to.emit(paymentGateway, "Deposit")
        .withArgs(user1.address, amount);
      
      // Charge should emit event
      const chargeAmount = ethers.utils.parseUnits("10", 6);
      await expect(paymentGateway.connect(owner).charge(user1.address, chargeAmount))
        .to.emit(paymentGateway, "PaymentMade")
        .withArgs(user1.address, chargeAmount);
      
      // Withdraw should emit event
      const withdrawAmount = ethers.utils.parseUnits("20", 6);
      await expect(paymentGateway.connect(user1).withdraw(withdrawAmount))
        .to.emit(paymentGateway, "Withdrawal")
        .withArgs(user1.address, withdrawAmount);
    });
  });
});
