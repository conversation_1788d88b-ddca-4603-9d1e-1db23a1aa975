# CryptoAI Smart Contracts

This directory contains the smart contracts for the CryptoAI Chat application, which enables cryptocurrency-based payments for AI interactions.

## Overview

The smart contract system provides two payment methods:
1. **Prepaid**: Users deposit tokens into the contract, and their balance is debited per interaction
2. **Permit (ERC-2612)**: Users sign gas-less authorizations, and the backend pulls funds as needed

## Contracts

### PaymentGateway.sol
The main contract that handles all payment operations.

**Key Features:**
- Prepaid balance management (deposit/withdraw)
- Owner-only charge functions for AI interactions
- ERC-2612 permit support for gas-less payments
- Comprehensive security measures (reentrancy protection, access control)
- Emergency functions for contract management

### PaymentGatewayV2.sol
Enhanced version with additional security features:
- Daily spending limits per user
- User and contract pause functionality
- Enhanced monitoring and control capabilities

### MockERC20Permit.sol
Mock ERC20 token with permit functionality for testing purposes.

## Setup

### Prerequisites
- Node.js >= 16
- npm or yarn

### Installation

```bash
# Install dependencies
npm install

# Copy environment file
cp .env.example .env

# Edit .env with your configuration
```

### Environment Variables

```bash
# Private key for deployment (without 0x prefix)
PRIVATE_KEY=your_private_key_here

# RPC URLs
SEPOLIA_RPC_URL=https://sepolia.infura.io/v3/YOUR_INFURA_KEY
MAINNET_RPC_URL=https://mainnet.infura.io/v3/YOUR_INFURA_KEY

# Etherscan API key for contract verification
ETHERSCAN_API_KEY=your_etherscan_api_key_here

# Payment token addresses
SEPOLIA_USDC_ADDRESS=******************************************
MAINNET_USDC_ADDRESS=******************************************
```

## Usage

### Compilation

```bash
npm run compile
```

### Testing

```bash
# Run all tests
npm test

# Run specific test file
npx hardhat test test/PaymentGateway.test.js

# Run tests with gas reporting
REPORT_GAS=true npm test

# Run coverage
npm run coverage
```

### Deployment

#### Local Network
```bash
# Start local Hardhat network
npx hardhat node

# Deploy to local network (in another terminal)
npx hardhat run scripts/deploy.js --network localhost
```

#### Sepolia Testnet
```bash
npm run deploy:sepolia
```

#### Mainnet
```bash
npm run deploy:mainnet
```

### Contract Interaction

```bash
# Interact with deployed contract
npx hardhat run scripts/interact.js --network sepolia
```

### Verification

```bash
# Verify contract on Etherscan
npx hardhat run scripts/verify.js --network sepolia
```

### Upgrade

```bash
# Deploy upgraded version
npx hardhat run scripts/upgrade.js --network sepolia
```

## Contract API

### PaymentGateway

#### User Functions

```solidity
// Deposit tokens into prepaid balance
function deposit(uint256 _amount) external

// Withdraw tokens from prepaid balance
function withdraw(uint256 _amount) external

// View prepaid balance
function getPrepaidBalance(address _user) external view returns (uint256)
```

#### Owner Functions

```solidity
// Charge user from prepaid balance
function charge(address _user, uint256 _amount) external onlyOwner

// Charge user using permit signature
function chargeWithPermit(
    address _user,
    uint256 _amount,
    uint256 _deadline,
    uint8 _v,
    bytes32 _r,
    bytes32 _s
) external onlyOwner

// Emergency withdraw contract funds
function emergencyWithdraw(uint256 _amount) external onlyOwner
```

## Security

### Implemented Security Measures

1. **Access Control**: OpenZeppelin Ownable for owner-only functions
2. **Reentrancy Protection**: ReentrancyGuard on all state-changing functions
3. **Input Validation**: Comprehensive validation of all inputs
4. **Error Handling**: Custom errors for gas-efficient error reporting
5. **Safe Transfers**: Proper handling of ERC20 transfers with failure detection

### Security Best Practices

- Use hardware wallets or multi-sig for owner accounts
- Regular security audits and code reviews
- Monitor contract events and transactions
- Keep dependencies updated
- Test thoroughly on testnets before mainnet deployment

See [SECURITY.md](./SECURITY.md) for detailed security analysis.

## Testing

The test suite includes:

- **Unit Tests**: Individual function testing
- **Integration Tests**: Complete user journey testing
- **Security Tests**: Access control and attack vector testing
- **Permit Tests**: ERC-2612 permit functionality testing
- **Edge Case Tests**: Boundary conditions and error scenarios

### Test Coverage

Run `npm run coverage` to generate a coverage report.

## Gas Optimization

The contracts are optimized for gas efficiency:

- Use of `immutable` variables where possible
- Efficient storage patterns
- Custom errors instead of string messages
- Optimized loops and operations

## Deployment Information

Deployment information is automatically saved to `deployments/` directory:

```
deployments/
├── sepolia-11155111.json
├── mainnet-1.json
└── localhost-31337.json
```

Each file contains:
- Contract addresses
- Deployment transaction details
- Network information
- Verification status
- Gas usage statistics

## Monitoring

### Events to Monitor

- `Deposit(address indexed user, uint256 amount)`
- `Withdrawal(address indexed user, uint256 amount)`
- `PaymentMade(address indexed user, uint256 amount)`
- `PermitPaymentMade(address indexed user, uint256 amount)`

### Key Metrics

- Contract token balance
- User prepaid balances
- Transaction volumes
- Failed transaction rates

## Troubleshooting

### Common Issues

1. **Deployment Fails**: Check network configuration and account balance
2. **Verification Fails**: Ensure correct constructor arguments and API key
3. **Tests Fail**: Check Hardhat network is running for local tests
4. **Gas Estimation Fails**: Check contract state and function parameters

### Support

For technical support:
1. Check the test files for usage examples
2. Review the security documentation
3. Examine deployment logs in the `deployments/` directory
4. Run the interaction script to test contract functionality

## License

MIT License - see LICENSE file for details.
