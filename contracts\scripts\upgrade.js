const { ethers } = require("hardhat");
const fs = require("fs");
const path = require("path");
require("dotenv").config();

async function main() {
  console.log("PaymentGateway Upgrade Script");
  console.log("=".repeat(50));
  
  // Get network information
  const network = await ethers.provider.getNetwork();
  console.log("Network:", network.name, "Chain ID:", network.chainId);
  
  // Load deployment info
  const deploymentsDir = path.join(__dirname, "..", "deployments");
  const deploymentFile = path.join(deploymentsDir, `${network.name}-${network.chainId}.json`);
  
  if (!fs.existsSync(deploymentFile)) {
    throw new Error(`Deployment file not found: ${deploymentFile}`);
  }
  
  const deploymentInfo = JSON.parse(fs.readFileSync(deploymentFile, 'utf8'));
  const oldContractAddress = deploymentInfo.contracts.paymentGateway.address;
  const paymentTokenAddress = deploymentInfo.paymentToken.address;
  
  console.log("Current Contract Address:", oldContractAddress);
  console.log("Payment Token:", paymentTokenAddress);
  
  // Get deployer
  const [deployer] = await ethers.getSigners();
  console.log("Deployer:", deployer.address);
  
  // Connect to old contract
  const oldContract = await ethers.getContractAt("PaymentGateway", oldContractAddress);
  const currentOwner = await oldContract.owner();
  
  if (currentOwner.toLowerCase() !== deployer.address.toLowerCase()) {
    throw new Error(`Deployer is not the contract owner. Owner: ${currentOwner}`);
  }
  
  console.log("✅ Deployer is contract owner");
  
  // Deploy new contract (PaymentGatewayV2)
  console.log("\n🚀 Deploying PaymentGatewayV2...");
  const PaymentGatewayV2 = await ethers.getContractFactory("PaymentGatewayV2");
  
  // Set daily spending limit (example: 1000 USDC)
  const dailySpendingLimit = ethers.utils.parseUnits("1000", 6);
  
  const newContract = await PaymentGatewayV2.deploy(paymentTokenAddress, dailySpendingLimit);
  await newContract.deployed();
  
  console.log("✅ PaymentGatewayV2 deployed to:", newContract.address);
  console.log("Daily Spending Limit:", ethers.utils.formatUnits(dailySpendingLimit, 6));
  
  // Get contract balance and user balances from old contract
  console.log("\n📊 Analyzing old contract state...");
  const contractTokenBalance = await ethers.getContractAt("IERC20", paymentTokenAddress)
    .then(token => token.balanceOf(oldContractAddress));
  
  console.log("Old contract token balance:", ethers.utils.formatUnits(contractTokenBalance, 6));
  
  // Emergency withdraw from old contract if there are funds
  if (contractTokenBalance.gt(0)) {
    console.log("\n💰 Emergency withdrawing funds from old contract...");
    const withdrawTx = await oldContract.emergencyWithdraw(contractTokenBalance);
    await withdrawTx.wait();
    console.log("✅ Funds withdrawn to owner");
  }
  
  // Transfer funds to new contract if needed
  if (contractTokenBalance.gt(0)) {
    console.log("\n💸 Transferring funds to new contract...");
    const token = await ethers.getContractAt("IERC20", paymentTokenAddress);
    const transferTx = await token.transfer(newContract.address, contractTokenBalance);
    await transferTx.wait();
    console.log("✅ Funds transferred to new contract");
  }
  
  // Update deployment info
  const upgradeInfo = {
    ...deploymentInfo,
    upgrades: deploymentInfo.upgrades || [],
    contracts: {
      ...deploymentInfo.contracts,
      paymentGatewayV2: {
        address: newContract.address,
        owner: await newContract.owner(),
        paymentToken: paymentTokenAddress,
        dailySpendingLimit: ethers.utils.formatUnits(dailySpendingLimit, 6),
        deployedAt: new Date().toISOString(),
        previousVersion: oldContractAddress
      }
    }
  };
  
  // Add upgrade record
  upgradeInfo.upgrades.push({
    from: oldContractAddress,
    to: newContract.address,
    version: "v2",
    timestamp: new Date().toISOString(),
    features: [
      "Daily spending limits",
      "User pause functionality", 
      "Contract pause functionality",
      "Enhanced security controls"
    ]
  });
  
  // Save updated deployment info
  fs.writeFileSync(deploymentFile, JSON.stringify(upgradeInfo, null, 2));
  console.log("\n📄 Updated deployment file");
  
  // Verify new contract if on public network
  if (network.chainId !== 31337 && process.env.ETHERSCAN_API_KEY) {
    console.log("\n🔍 Verifying new contract...");
    try {
      await newContract.deployTransaction.wait(6);
      await hre.run("verify:verify", {
        address: newContract.address,
        constructorArguments: [paymentTokenAddress, dailySpendingLimit],
      });
      console.log("✅ Contract verified successfully!");
    } catch (error) {
      console.log("❌ Verification failed:", error.message);
    }
  }
  
  console.log("\n🎉 Upgrade completed successfully!");
  console.log("=".repeat(50));
  console.log("Old Contract:", oldContractAddress);
  console.log("New Contract:", newContract.address);
  console.log("New Features:");
  console.log("  - Daily spending limits");
  console.log("  - User pause functionality");
  console.log("  - Contract pause functionality");
  console.log("  - Enhanced security controls");
  
  console.log("\n📋 Next Steps:");
  console.log("1. Update backend configuration to use new contract address");
  console.log("2. Update frontend configuration to use new contract address");
  console.log("3. Notify users about the upgrade");
  console.log("4. Monitor new contract for any issues");
  console.log("5. Consider deprecating old contract after migration period");
  
  console.log("\n⚠️  Important Notes:");
  console.log("- Users will need to deposit funds into the new contract");
  console.log("- Old prepaid balances are not automatically migrated");
  console.log("- Consider implementing a migration function for user convenience");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
