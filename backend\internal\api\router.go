package api

import (
	// "github.com/gin-gonic/gin"
)

// Placeholder for API handlers
func getUserBalanceHandler() {}
func submitPermitHandler() {}
func chatHandler() {}


func NewRouter() /* *gin.Engine */ {
	// router := gin.Default()

	// v1 := router.Group("/api/v1")
	// {
	// 	v1.GET("/balance/:address", getUserBalanceHandler)
	// 	v1.POST("/permit", submitPermitHandler)
	// 	v1.POST("/chat", chatHandler)
	// }

	// return router
	return nil
}
