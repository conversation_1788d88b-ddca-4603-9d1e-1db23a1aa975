// import axios from 'axios';

const apiClient = {
    postChatMessage: async (prompt: string, userAddress: string) => {
        // TODO: POST /api/v1/chat
    },

    submitPermitSignature: async (signatureData: any) => {
        // TODO: POST /api/v1/permit
    },

    getBalance: async (userAddress: string) => {
        // TODO: GET /api/v1/balance/{userAddress}
    }
};

export default apiClient;
