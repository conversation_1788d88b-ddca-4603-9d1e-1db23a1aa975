// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "./PaymentGateway.sol";

/**
 * @title PaymentGatewayV2
 * @dev Enhanced version of PaymentGateway with additional security features
 * This contract demonstrates how to add new features while maintaining compatibility
 */
contract PaymentGatewayV2 is PaymentGateway {
    // --- Additional State Variables ---
    mapping(address => bool) public pausedUsers;
    mapping(address => uint256) public dailySpentAmount;
    mapping(address => uint256) public lastSpendingDay;
    
    uint256 public dailySpendingLimit;
    bool public contractPaused;
    
    // --- Additional Events ---
    event UserPaused(address indexed user);
    event UserUnpaused(address indexed user);
    event ContractPaused();
    event ContractUnpaused();
    event DailySpendingLimitUpdated(uint256 newLimit);
    event DailySpendingLimitExceeded(address indexed user, uint256 attempted, uint256 limit);

    // --- Additional Errors ---
    error UserPaused();
    error ContractPaused();
    error DailySpendingLimitExceeded(uint256 attempted, uint256 limit);

    // --- Modifiers ---
    modifier whenNotPaused() {
        if (contractPaused) {
            revert ContractPaused();
        }
        _;
    }

    modifier whenUserNotPaused(address user) {
        if (pausedUsers[user]) {
            revert UserPaused();
        }
        _;
    }

    // --- Constructor ---
    constructor(address _paymentTokenAddress, uint256 _dailySpendingLimit) 
        PaymentGateway(_paymentTokenAddress) {
        dailySpendingLimit = _dailySpendingLimit;
    }

    // --- Enhanced Functions ---
    
    /**
     * @dev Enhanced deposit function with pause checks
     */
    function deposit(uint256 _amount) 
        external 
        override 
        nonReentrant 
        whenNotPaused 
        whenUserNotPaused(msg.sender) 
    {
        super.deposit(_amount);
    }

    /**
     * @dev Enhanced withdraw function with pause checks
     */
    function withdraw(uint256 _amount) 
        external 
        override 
        nonReentrant 
        whenNotPaused 
        whenUserNotPaused(msg.sender) 
    {
        super.withdraw(_amount);
    }

    /**
     * @dev Enhanced charge function with daily spending limits
     */
    function charge(address _user, uint256 _amount) 
        external 
        override 
        onlyOwner 
        nonReentrant 
        whenNotPaused 
        whenUserNotPaused(_user) 
    {
        _checkDailySpendingLimit(_user, _amount);
        super.charge(_user, _amount);
        _updateDailySpending(_user, _amount);
    }

    /**
     * @dev Enhanced chargeWithPermit function with daily spending limits
     */
    function chargeWithPermit(
        address _user,
        uint256 _amount,
        uint256 _deadline,
        uint8 _v,
        bytes32 _r,
        bytes32 _s
    ) 
        external 
        override 
        onlyOwner 
        nonReentrant 
        whenNotPaused 
        whenUserNotPaused(_user) 
    {
        _checkDailySpendingLimit(_user, _amount);
        super.chargeWithPermit(_user, _amount, _deadline, _v, _r, _s);
        _updateDailySpending(_user, _amount);
    }

    // --- New Security Functions ---

    /**
     * @dev Pause the entire contract (emergency function)
     */
    function pauseContract() external onlyOwner {
        contractPaused = true;
        emit ContractPaused();
    }

    /**
     * @dev Unpause the entire contract
     */
    function unpauseContract() external onlyOwner {
        contractPaused = false;
        emit ContractUnpaused();
    }

    /**
     * @dev Pause a specific user (emergency function)
     */
    function pauseUser(address _user) external onlyOwner {
        pausedUsers[_user] = true;
        emit UserPaused(_user);
    }

    /**
     * @dev Unpause a specific user
     */
    function unpauseUser(address _user) external onlyOwner {
        pausedUsers[_user] = false;
        emit UserUnpaused(_user);
    }

    /**
     * @dev Update daily spending limit
     */
    function setDailySpendingLimit(uint256 _newLimit) external onlyOwner {
        dailySpendingLimit = _newLimit;
        emit DailySpendingLimitUpdated(_newLimit);
    }

    /**
     * @dev Get user's remaining daily spending allowance
     */
    function getRemainingDailyAllowance(address _user) external view returns (uint256) {
        uint256 currentDay = block.timestamp / 1 days;
        
        if (lastSpendingDay[_user] != currentDay) {
            return dailySpendingLimit;
        }
        
        if (dailySpentAmount[_user] >= dailySpendingLimit) {
            return 0;
        }
        
        return dailySpendingLimit - dailySpentAmount[_user];
    }

    /**
     * @dev Get user's daily spending information
     */
    function getDailySpendingInfo(address _user) external view returns (
        uint256 spentToday,
        uint256 limit,
        uint256 remaining,
        uint256 lastDay
    ) {
        uint256 currentDay = block.timestamp / 1 days;
        
        if (lastSpendingDay[_user] != currentDay) {
            spentToday = 0;
        } else {
            spentToday = dailySpentAmount[_user];
        }
        
        limit = dailySpendingLimit;
        remaining = limit > spentToday ? limit - spentToday : 0;
        lastDay = lastSpendingDay[_user];
    }

    // --- Internal Functions ---

    /**
     * @dev Check if user can spend the requested amount within daily limit
     */
    function _checkDailySpendingLimit(address _user, uint256 _amount) internal view {
        if (dailySpendingLimit == 0) return; // No limit set
        
        uint256 currentDay = block.timestamp / 1 days;
        uint256 todaySpent = 0;
        
        if (lastSpendingDay[_user] == currentDay) {
            todaySpent = dailySpentAmount[_user];
        }
        
        if (todaySpent + _amount > dailySpendingLimit) {
            revert DailySpendingLimitExceeded(_amount, dailySpendingLimit - todaySpent);
        }
    }

    /**
     * @dev Update user's daily spending amount
     */
    function _updateDailySpending(address _user, uint256 _amount) internal {
        uint256 currentDay = block.timestamp / 1 days;
        
        if (lastSpendingDay[_user] != currentDay) {
            dailySpentAmount[_user] = _amount;
            lastSpendingDay[_user] = currentDay;
        } else {
            dailySpentAmount[_user] += _amount;
        }
    }
}
