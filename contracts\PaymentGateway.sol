// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/IERC20Permit.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

/**
 * @title PaymentGateway
 * @dev Smart contract for handling crypto payments in the CryptoAI Chat application
 * Supports both prepaid deposits and ERC-2612 permit-based payments
 */
contract PaymentGateway is ReentrancyGuard, Ownable {
    // --- State Variables ---
    IERC20Permit public immutable paymentToken;
    mapping(address => uint256) public prepaidBalances;

    // --- Events ---
    event Deposit(address indexed user, uint256 amount);
    event Withdrawal(address indexed user, uint256 amount);
    event PaymentMade(address indexed user, uint256 amount);
    event PermitPaymentMade(address indexed user, uint256 amount);

    // --- Errors ---
    error InsufficientBalance(uint256 requested, uint256 available);
    error InvalidAmount();
    error TransferFailed();
    error InvalidToken();

    // --- Constructor ---
    constructor(address _paymentTokenAddress) {
        if (_paymentTokenAddress == address(0)) {
            revert InvalidToken();
        }
        paymentToken = IERC20Permit(_paymentTokenAddress);
    }

    // --- Prepaid Functions ---

    /**
     * @dev Allows users to deposit tokens into their prepaid balance
     * @param _amount Amount of tokens to deposit
     */
    function deposit(uint256 _amount) external nonReentrant {
        if (_amount == 0) {
            revert InvalidAmount();
        }

        // Transfer tokens from user to contract
        bool success = paymentToken.transferFrom(msg.sender, address(this), _amount);
        if (!success) {
            revert TransferFailed();
        }

        // Update user's prepaid balance
        prepaidBalances[msg.sender] += _amount;

        emit Deposit(msg.sender, _amount);
    }

    /**
     * @dev Allows users to withdraw tokens from their prepaid balance
     * @param _amount Amount of tokens to withdraw
     */
    function withdraw(uint256 _amount) external nonReentrant {
        if (_amount == 0) {
            revert InvalidAmount();
        }

        uint256 userBalance = prepaidBalances[msg.sender];
        if (userBalance < _amount) {
            revert InsufficientBalance(_amount, userBalance);
        }

        // Update user's prepaid balance first (CEI pattern)
        prepaidBalances[msg.sender] = userBalance - _amount;

        // Transfer tokens from contract to user
        bool success = paymentToken.transfer(msg.sender, _amount);
        if (!success) {
            revert TransferFailed();
        }

        emit Withdrawal(msg.sender, _amount);
    }

    // --- Core Payment Logic ---

    /**
     * @dev Charges a user from their prepaid balance (only callable by owner/backend)
     * @param _user Address of the user to charge
     * @param _amount Amount to charge
     */
    function charge(address _user, uint256 _amount) external onlyOwner nonReentrant {
        if (_amount == 0) {
            revert InvalidAmount();
        }

        uint256 userBalance = prepaidBalances[_user];
        if (userBalance < _amount) {
            revert InsufficientBalance(_amount, userBalance);
        }

        // Deduct from user's prepaid balance
        prepaidBalances[_user] = userBalance - _amount;

        emit PaymentMade(_user, _amount);
    }

    /**
     * @dev Charges a user using ERC-2612 permit signature (only callable by owner/backend)
     * @param _user Address of the user to charge
     * @param _amount Amount to charge
     * @param _deadline Permit deadline timestamp
     * @param _v Recovery byte of the signature
     * @param _r First 32 bytes of the signature
     * @param _s Second 32 bytes of the signature
     */
    function chargeWithPermit(
        address _user,
        uint256 _amount,
        uint256 _deadline,
        uint8 _v,
        bytes32 _r,
        bytes32 _s
    ) external onlyOwner nonReentrant {
        if (_amount == 0) {
            revert InvalidAmount();
        }

        // Use the permit signature to approve the transfer
        paymentToken.permit(_user, address(this), _amount, _deadline, _v, _r, _s);

        // Transfer tokens directly from user's wallet to contract
        bool success = paymentToken.transferFrom(_user, address(this), _amount);
        if (!success) {
            revert TransferFailed();
        }

        emit PermitPaymentMade(_user, _amount);
    }

    // --- View Functions ---

    /**
     * @dev Returns the prepaid balance of a user
     * @param _user Address of the user
     * @return The user's prepaid balance
     */
    function getPrepaidBalance(address _user) external view returns (uint256) {
        return prepaidBalances[_user];
    }

    /**
     * @dev Returns the address of the payment token
     * @return The payment token contract address
     */
    function getPaymentToken() external view returns (address) {
        return address(paymentToken);
    }

    // --- Emergency Functions ---

    /**
     * @dev Emergency function to withdraw contract's token balance (only owner)
     * @param _amount Amount to withdraw
     */
    function emergencyWithdraw(uint256 _amount) external onlyOwner {
        if (_amount == 0) {
            revert InvalidAmount();
        }

        bool success = paymentToken.transfer(owner(), _amount);
        if (!success) {
            revert TransferFailed();
        }
    }
}
