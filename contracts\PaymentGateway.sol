// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Permit.sol";

contract PaymentGateway {
    // --- State Variables ---
    address public owner;
    IERC20 public immutable paymentToken;
    mapping(address => uint256) public prepaidBalances;

    // --- Events ---
    event Deposit(address indexed user, uint256 amount);
    event Withdrawal(address indexed user, uint256 amount);
    event PaymentMade(address indexed user, uint256 amount);

    // --- Constructor ---
    constructor(address _paymentTokenAddress) {
        // TODO: Initialize owner and paymentToken
    }

    // --- Prepaid Functions ---
    function deposit(uint256 _amount) external {
        // TODO: Transfer token from user to contract, update prepaidBalances
    }

    function withdraw(uint256 _amount) external {
        // TODO: Check balance, transfer token from contract to user, update prepaidBalances
    }

    // --- Core Payment Logic ---
    function charge(address _user, uint256 _amount) external {
        // TODO: Only allow owner (backend) to call this.
        // TODO: Deduct from _user's prepaidBalance.
    }

    function chargeWithPermit(
        address _user,
        uint256 _amount,
        uint256 _deadline,
        uint8 _v,
        bytes32 _r,
        bytes32 _s
    ) external {
        // TODO: Only allow owner (backend) to call this.
        // TODO: Use permit signature to pull funds directly from user's wallet.
        // TODO: Call paymentToken.permit(...)
        // TODO: Call paymentToken.transferFrom(...)
    }

    // --- View Functions ---
    function getPrepaidBalance(address _user) external view returns (uint256) {
        // TODO: Return prepaidBalances[_user]
    }
}
