const { expect } = require("chai");
const { ethers } = require("hardhat");
const { loadFixture, time } = require("@nomicfoundation/hardhat-network-helpers");

describe("PaymentGateway - Integration Tests", function () {
  // Comprehensive fixture for integration testing
  async function deployIntegrationFixture() {
    const [owner, user1, user2, user3, backend] = await ethers.getSigners();

    // Deploy mock ERC20 token with permit
    const MockToken = await ethers.getContractFactory("MockERC20Permit");
    const mockToken = await MockToken.deploy("Mock USDC", "MUSDC", 6);

    // Deploy PaymentGateway
    const PaymentGateway = await ethers.getContractFactory("PaymentGateway");
    const paymentGateway = await PaymentGateway.deploy(mockToken.address);

    // Mint tokens to users
    const mintAmount = ethers.utils.parseUnits("1000", 6);
    await mockToken.mint(user1.address, mintAmount);
    await mockToken.mint(user2.address, mintAmount);
    await mockToken.mint(user3.address, mintAmount);

    // Transfer ownership to backend (simulating real-world scenario)
    await paymentGateway.connect(owner).transferOwnership(backend.address);

    return { paymentGateway, mockToken, owner, user1, user2, user3, backend, mintAmount };
  }

  // Helper function for permit signatures
  async function createPermitSignature(token, owner, spender, value, deadline, signer) {
    const nonce = await token.nonces(owner.address);
    const name = await token.name();
    const version = "1";
    const chainId = await signer.getChainId();

    const domain = {
      name,
      version,
      chainId,
      verifyingContract: token.address,
    };

    const types = {
      Permit: [
        { name: "owner", type: "address" },
        { name: "spender", type: "address" },
        { name: "value", type: "uint256" },
        { name: "nonce", type: "uint256" },
        { name: "deadline", type: "uint256" },
      ],
    };

    const values = {
      owner: owner.address,
      spender: spender,
      value,
      nonce,
      deadline,
    };

    const signature = await signer._signTypedData(domain, types, values);
    const { v, r, s } = ethers.utils.splitSignature(signature);

    return { v, r, s, deadline, nonce };
  }

  describe("Complete User Journey - Prepaid Flow", function () {
    it("Should handle complete prepaid user journey", async function () {
      const { paymentGateway, mockToken, user1, backend } = await loadFixture(deployIntegrationFixture);
      
      // Step 1: User deposits funds
      const depositAmount = ethers.utils.parseUnits("200", 6);
      await mockToken.connect(user1).approve(paymentGateway.address, depositAmount);
      await paymentGateway.connect(user1).deposit(depositAmount);
      
      expect(await paymentGateway.getPrepaidBalance(user1.address)).to.equal(depositAmount);
      
      // Step 2: Backend charges for AI interactions
      const chatCost1 = ethers.utils.parseUnits("10", 6);
      const chatCost2 = ethers.utils.parseUnits("15", 6);
      const chatCost3 = ethers.utils.parseUnits("8", 6);
      
      await paymentGateway.connect(backend).charge(user1.address, chatCost1);
      await paymentGateway.connect(backend).charge(user1.address, chatCost2);
      await paymentGateway.connect(backend).charge(user1.address, chatCost3);
      
      const expectedBalance = depositAmount.sub(chatCost1).sub(chatCost2).sub(chatCost3);
      expect(await paymentGateway.getPrepaidBalance(user1.address)).to.equal(expectedBalance);
      
      // Step 3: User withdraws remaining balance
      const withdrawAmount = expectedBalance;
      const initialTokenBalance = await mockToken.balanceOf(user1.address);
      
      await paymentGateway.connect(user1).withdraw(withdrawAmount);
      
      expect(await paymentGateway.getPrepaidBalance(user1.address)).to.equal(0);
      expect(await mockToken.balanceOf(user1.address)).to.equal(initialTokenBalance.add(withdrawAmount));
    });

    it("Should handle multiple users with prepaid balances", async function () {
      const { paymentGateway, mockToken, user1, user2, user3, backend } = await loadFixture(deployIntegrationFixture);
      
      // Multiple users deposit different amounts
      const deposit1 = ethers.utils.parseUnits("100", 6);
      const deposit2 = ethers.utils.parseUnits("200", 6);
      const deposit3 = ethers.utils.parseUnits("150", 6);
      
      await mockToken.connect(user1).approve(paymentGateway.address, deposit1);
      await paymentGateway.connect(user1).deposit(deposit1);
      
      await mockToken.connect(user2).approve(paymentGateway.address, deposit2);
      await paymentGateway.connect(user2).deposit(deposit2);
      
      await mockToken.connect(user3).approve(paymentGateway.address, deposit3);
      await paymentGateway.connect(user3).deposit(deposit3);
      
      // Backend charges different amounts from each user
      await paymentGateway.connect(backend).charge(user1.address, ethers.utils.parseUnits("30", 6));
      await paymentGateway.connect(backend).charge(user2.address, ethers.utils.parseUnits("50", 6));
      await paymentGateway.connect(backend).charge(user3.address, ethers.utils.parseUnits("25", 6));
      
      // Verify individual balances
      expect(await paymentGateway.getPrepaidBalance(user1.address))
        .to.equal(deposit1.sub(ethers.utils.parseUnits("30", 6)));
      expect(await paymentGateway.getPrepaidBalance(user2.address))
        .to.equal(deposit2.sub(ethers.utils.parseUnits("50", 6)));
      expect(await paymentGateway.getPrepaidBalance(user3.address))
        .to.equal(deposit3.sub(ethers.utils.parseUnits("25", 6)));
    });
  });

  describe("Complete User Journey - Permit Flow", function () {
    it("Should handle complete permit-based payment flow", async function () {
      const { paymentGateway, mockToken, user1, backend } = await loadFixture(deployIntegrationFixture);
      
      const initialUserBalance = await mockToken.balanceOf(user1.address);
      const initialContractBalance = await mockToken.balanceOf(paymentGateway.address);
      
      // Simulate multiple AI interactions using permit
      const chatCosts = [
        ethers.utils.parseUnits("12", 6),
        ethers.utils.parseUnits("18", 6),
        ethers.utils.parseUnits("9", 6),
        ethers.utils.parseUnits("15", 6),
      ];
      
      let totalCost = ethers.BigNumber.from(0);
      
      for (let i = 0; i < chatCosts.length; i++) {
        const cost = chatCosts[i];
        const deadline = (await time.latest()) + 3600;
        
        const { v, r, s } = await createPermitSignature(
          mockToken,
          user1,
          paymentGateway.address,
          cost,
          deadline,
          user1
        );
        
        await paymentGateway.connect(backend).chargeWithPermit(
          user1.address,
          cost,
          deadline,
          v,
          r,
          s
        );
        
        totalCost = totalCost.add(cost);
      }
      
      // Verify final balances
      expect(await mockToken.balanceOf(user1.address))
        .to.equal(initialUserBalance.sub(totalCost));
      expect(await mockToken.balanceOf(paymentGateway.address))
        .to.equal(initialContractBalance.add(totalCost));
    });
  });

  describe("Mixed Payment Scenarios", function () {
    it("Should handle user switching between prepaid and permit payments", async function () {
      const { paymentGateway, mockToken, user1, backend } = await loadFixture(deployIntegrationFixture);
      
      // Start with prepaid deposit
      const depositAmount = ethers.utils.parseUnits("100", 6);
      await mockToken.connect(user1).approve(paymentGateway.address, depositAmount);
      await paymentGateway.connect(user1).deposit(depositAmount);
      
      // Use prepaid for some interactions
      await paymentGateway.connect(backend).charge(user1.address, ethers.utils.parseUnits("30", 6));
      await paymentGateway.connect(backend).charge(user1.address, ethers.utils.parseUnits("20", 6));
      
      // Switch to permit-based payments
      const permitAmount = ethers.utils.parseUnits("25", 6);
      const deadline = (await time.latest()) + 3600;
      
      const { v, r, s } = await createPermitSignature(
        mockToken,
        user1,
        paymentGateway.address,
        permitAmount,
        deadline,
        user1
      );
      
      await paymentGateway.connect(backend).chargeWithPermit(
        user1.address,
        permitAmount,
        deadline,
        v,
        r,
        s
      );
      
      // Verify prepaid balance is unchanged by permit payment
      const expectedPrepaidBalance = depositAmount.sub(ethers.utils.parseUnits("50", 6));
      expect(await paymentGateway.getPrepaidBalance(user1.address)).to.equal(expectedPrepaidBalance);
    });
  });

  describe("Gas Optimization Scenarios", function () {
    it("Should demonstrate gas efficiency of prepaid vs permit", async function () {
      const { paymentGateway, mockToken, user1, user2, backend } = await loadFixture(deployIntegrationFixture);
      
      // Setup user1 with prepaid balance
      const depositAmount = ethers.utils.parseUnits("200", 6);
      await mockToken.connect(user1).approve(paymentGateway.address, depositAmount);
      await paymentGateway.connect(user1).deposit(depositAmount);
      
      const chargeAmount = ethers.utils.parseUnits("10", 6);
      
      // Measure gas for prepaid charge
      const prepaidTx = await paymentGateway.connect(backend).charge(user1.address, chargeAmount);
      const prepaidReceipt = await prepaidTx.wait();
      
      // Measure gas for permit charge
      const deadline = (await time.latest()) + 3600;
      const { v, r, s } = await createPermitSignature(
        mockToken,
        user2,
        paymentGateway.address,
        chargeAmount,
        deadline,
        user2
      );
      
      const permitTx = await paymentGateway.connect(backend).chargeWithPermit(
        user2.address,
        chargeAmount,
        deadline,
        v,
        r,
        s
      );
      const permitReceipt = await permitTx.wait();
      
      console.log(`Prepaid charge gas: ${prepaidReceipt.gasUsed}`);
      console.log(`Permit charge gas: ${permitReceipt.gasUsed}`);
      
      // Prepaid should use less gas for subsequent charges
      expect(prepaidReceipt.gasUsed.lt(permitReceipt.gasUsed)).to.be.true;
    });
  });
});
